# 攀岩教练预约iOS应用开发指南

## 1. 项目概述

### 1.1 应用定位
基于现有微信小程序的攀岩教练预约平台，开发对应的iOS原生应用，为用户提供更好的移动端体验和更丰富的功能。

### 1.2 核心功能对标
- **学员端**：教练搜索、详情查看、预约管理、评价系统
- **教练端**：个人资料管理、课程发布、时间管理、预约处理、收入统计
- **通用功能**：用户认证、消息推送、支付集成、地理位置服务

### 1.3 技术优势
- 原生性能体验
- 更丰富的系统集成（通知、相机、地图等）
- 离线功能支持
- 更好的用户界面适配

---

## 2. 技术架构设计

### 2.1 技术栈选择

#### 2.1.1 开发框架
**推荐方案：SwiftUI + Combine**
- **SwiftUI**：现代化UI框架，声明式编程
- **Combine**：响应式编程框架，处理异步数据流
- **Swift 5.5+**：利用async/await等现代语言特性

**备选方案：UIKit + RxSwift**
- 适合团队已有UIKit经验的情况
- RxSwift提供响应式编程支持

#### 2.1.2 架构模式
**MVVM + Coordinator Pattern**
```
View (SwiftUI) ↔ ViewModel ↔ Model
                     ↕
                Coordinator (导航管理)
                     ↕
                Service Layer (网络、数据)
```

#### 2.1.3 核心依赖库
```swift
// 网络请求
Alamofire (~> 5.6)

// 图片加载
Kingfisher (~> 7.0)

// 数据持久化
CoreData / Realm

// 地图服务
MapKit (系统自带)

// 支付集成
StoreKit (应用内购买)
// 或第三方支付SDK（支付宝、微信支付）

// 推送通知
UserNotifications (系统自带)

// 日志记录
CocoaLumberjack (~> 3.7)

// 键值存储
KeychainAccess (~> 4.2)
```

### 2.2 项目结构

```
ClimbingCoachApp/
├── App/
│   ├── AppDelegate.swift
│   ├── SceneDelegate.swift
│   └── ClimbingCoachApp.swift
├── Core/
│   ├── Network/
│   │   ├── APIClient.swift
│   │   ├── APIEndpoints.swift
│   │   └── NetworkError.swift
│   ├── Storage/
│   │   ├── CoreDataStack.swift
│   │   ├── UserDefaults+Extensions.swift
│   │   └── KeychainManager.swift
│   ├── Utils/
│   │   ├── Extensions/
│   │   ├── Constants.swift
│   │   └── Logger.swift
│   └── Coordinators/
│       ├── AppCoordinator.swift
│       ├── StudentCoordinator.swift
│       └── CoachCoordinator.swift
├── Features/
│   ├── Authentication/
│   ├── Student/
│   │   ├── CoachDiscovery/
│   │   ├── CoachDetail/
│   │   ├── Booking/
│   │   └── MyBookings/
│   ├── Coach/
│   │   ├── Dashboard/
│   │   ├── Profile/
│   │   ├── Schedule/
│   │   └── Bookings/
│   └── Shared/
│       ├── Components/
│       ├── Models/
│       └── ViewModels/
├── Resources/
│   ├── Assets.xcassets
│   ├── Localizable.strings
│   └── Info.plist
└── Tests/
    ├── UnitTests/
    └── UITests/
```

---

## 3. 数据层设计

### 3.1 API接口设计

#### 3.1.1 基础配置
```swift
struct APIConfig {
    static let baseURL = "https://your-backend-api.com/api/v1"
    static let timeout: TimeInterval = 30
}

enum APIEndpoint {
    case login(phone: String, code: String)
    case searchCoaches(filters: CoachFilters)
    case getCoachDetail(id: String)
    case createBooking(booking: BookingRequest)
    case getMyBookings
    // ... 其他接口
}
```

#### 3.1.2 数据模型
```swift
// 教练模型
struct Coach: Codable, Identifiable {
    let id: String
    let name: String
    let avatar: String?
    let city: String
    let gym: String
    let boulderingLevel: String
    let leadLevel: String
    let certifications: [String]
    let specialty: [String]
    let style: String
    let intro: String
    let rating: Double
    let reviewCount: Int
    let price: Int
    let needDeposit: Bool
    let depositAmount: Int?
    let courses: [Course]
    let availability: [TimeSlot]
}

// 预约模型
struct Booking: Codable, Identifiable {
    let id: String
    let coachId: String
    let courseId: String
    let timeSlot: TimeSlot
    let status: BookingStatus
    let studentName: String
    let studentPhone: String
    let depositAmount: Int
    let depositStatus: DepositStatus
    let createdAt: Date
}

enum BookingStatus: String, Codable, CaseIterable {
    case pending = "待确认"
    case confirmed = "已确认"
    case completed = "已完成"
    case cancelled = "已取消"
}
```

### 3.2 本地数据存储

#### 3.2.1 CoreData模型
```swift
// Coach+CoreDataClass.swift
@objc(Coach)
public class Coach: NSManagedObject {
    // 实现缓存逻辑
}

// 数据同步策略
class DataSyncManager {
    func syncCoaches() async throws {
        // 从API获取数据
        // 更新本地缓存
        // 处理冲突
    }
}
```

---

## 4. 核心功能模块开发

### 4.1 用户认证模块

#### 4.1.1 登录流程
```swift
class AuthenticationViewModel: ObservableObject {
    @Published var isAuthenticated = false
    @Published var userType: UserType?
    
    func login(phone: String, verificationCode: String) async {
        // 调用登录API
        // 保存用户信息和token
        // 更新认证状态
    }
    
    func logout() {
        // 清除本地数据
        // 更新UI状态
    }
}

struct LoginView: View {
    @StateObject private var viewModel = AuthenticationViewModel()
    
    var body: some View {
        // SwiftUI登录界面
    }
}
```

#### 4.1.2 权限管理
```swift
enum UserType: String, Codable {
    case student = "student"
    case coach = "coach"
}

class PermissionManager {
    static func hasCoachPermission() -> Bool {
        return UserManager.shared.currentUser?.type == .coach
    }
}
```

### 4.2 学员端功能

#### 4.2.1 教练搜索与筛选
```swift
class CoachDiscoveryViewModel: ObservableObject {
    @Published var coaches: [Coach] = []
    @Published var filters = CoachFilters()
    @Published var isLoading = false
    
    func searchCoaches() async {
        isLoading = true
        defer { isLoading = false }
        
        do {
            let result = try await APIClient.shared.searchCoaches(filters: filters)
            await MainActor.run {
                self.coaches = result
            }
        } catch {
            // 错误处理
        }
    }
}

struct CoachDiscoveryView: View {
    @StateObject private var viewModel = CoachDiscoveryViewModel()
    
    var body: some View {
        NavigationView {
            VStack {
                // 搜索栏
                SearchBar(text: $viewModel.filters.keyword)
                
                // 筛选器
                FilterView(filters: $viewModel.filters)
                
                // 教练列表
                LazyVStack {
                    ForEach(viewModel.coaches) { coach in
                        CoachCardView(coach: coach)
                    }
                }
            }
        }
        .task {
            await viewModel.searchCoaches()
        }
    }
}
```

#### 4.2.2 预约功能
```swift
class BookingViewModel: ObservableObject {
    @Published var selectedTimeSlot: TimeSlot?
    @Published var studentInfo = StudentInfo()
    @Published var isSubmitting = false
    
    func createBooking() async {
        guard let timeSlot = selectedTimeSlot else { return }
        
        isSubmitting = true
        defer { isSubmitting = false }
        
        let booking = BookingRequest(
            coachId: coach.id,
            timeSlot: timeSlot,
            studentInfo: studentInfo
        )
        
        do {
            try await APIClient.shared.createBooking(booking)
            // 跳转到支付或成功页面
        } catch {
            // 错误处理
        }
    }
}
```

### 4.3 教练端功能

#### 4.3.1 时间管理
```swift
class ScheduleViewModel: ObservableObject {
    @Published var availableSlots: [TimeSlot] = []
    @Published var selectedDate = Date()
    
    func addTimeSlot(_ slot: TimeSlot) async {
        do {
            try await APIClient.shared.addTimeSlot(slot)
            await loadSchedule()
        } catch {
            // 错误处理
        }
    }
    
    func removeTimeSlot(_ slot: TimeSlot) async {
        // 删除时间段逻辑
    }
}

struct ScheduleView: View {
    @StateObject private var viewModel = ScheduleViewModel()
    
    var body: some View {
        VStack {
            // 日历组件
            CalendarView(selectedDate: $viewModel.selectedDate)
            
            // 时间段列表
            TimeSlotListView(
                slots: viewModel.availableSlots,
                onAdd: viewModel.addTimeSlot,
                onRemove: viewModel.removeTimeSlot
            )
        }
    }
}
```

---

## 5. UI/UX设计指南

### 5.1 设计系统

#### 5.1.1 颜色规范
```swift
extension Color {
    static let primaryBlue = Color(red: 0.2, green: 0.6, blue: 1.0)
    static let secondaryOrange = Color(red: 1.0, green: 0.6, blue: 0.2)
    static let backgroundGray = Color(red: 0.95, green: 0.95, blue: 0.97)
    static let textPrimary = Color(red: 0.2, green: 0.2, blue: 0.2)
    static let textSecondary = Color(red: 0.6, green: 0.6, blue: 0.6)
}
```

#### 5.1.2 字体规范
```swift
extension Font {
    static let titleLarge = Font.system(size: 24, weight: .bold)
    static let titleMedium = Font.system(size: 20, weight: .semibold)
    static let bodyLarge = Font.system(size: 16, weight: .regular)
    static let bodyMedium = Font.system(size: 14, weight: .regular)
    static let caption = Font.system(size: 12, weight: .regular)
}
```

### 5.2 关键页面设计

#### 5.2.1 教练卡片组件
```swift
struct CoachCardView: View {
    let coach: Coach
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                AsyncImage(url: URL(string: coach.avatar ?? "")) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                } placeholder: {
                    Circle()
                        .fill(Color.gray.opacity(0.3))
                }
                .frame(width: 60, height: 60)
                .clipShape(Circle())
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(coach.name)
                        .font(.titleMedium)
                        .foregroundColor(.textPrimary)
                    
                    HStack {
                        Image(systemName: "star.fill")
                            .foregroundColor(.yellow)
                        Text("\(coach.rating, specifier: "%.1f")")
                        Text("(\(coach.reviewCount)条评价)")
                            .foregroundColor(.textSecondary)
                    }
                    .font(.caption)
                }
                
                Spacer()
                
                VStack(alignment: .trailing) {
                    Text("¥\(coach.price)/节")
                        .font(.bodyLarge)
                        .fontWeight(.semibold)
                    
                    if coach.needDeposit {
                        Text("需定金")
                            .font(.caption)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 2)
                            .background(Color.orange.opacity(0.2))
                            .foregroundColor(.orange)
                            .cornerRadius(4)
                    }
                }
            }
            
            // 能力标签
            HStack {
                TagView(text: "抱石: \(coach.boulderingLevel)")
                TagView(text: "先锋: \(coach.leadLevel)")
                Spacer()
            }
            
            // 专长和地点
            Text(coach.specialty.joined(separator: "、"))
                .font(.bodyMedium)
                .foregroundColor(.textSecondary)
            
            Text("常驻：\(coach.gym)")
                .font(.caption)
                .foregroundColor(.textSecondary)
        }
        .padding()
        .background(Color.white)
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
}
```

---

## 6. 系统集成功能

### 6.1 推送通知
```swift
class NotificationManager {
    static let shared = NotificationManager()
    
    func requestPermission() async {
        let center = UNUserNotificationCenter.current()
        do {
            let granted = try await center.requestAuthorization(options: [.alert, .sound, .badge])
            if granted {
                await registerForRemoteNotifications()
            }
        } catch {
            print("通知权限请求失败: \(error)")
        }
    }
    
    func scheduleBookingReminder(for booking: Booking) {
        // 预约提醒逻辑
    }
}
```

### 6.2 地图集成
```swift
struct CoachLocationView: View {
    let coaches: [Coach]
    @State private var region = MKCoordinateRegion()
    
    var body: some View {
        Map(coordinateRegion: $region, annotationItems: coaches) { coach in
            MapAnnotation(coordinate: coach.coordinate) {
                CoachMapMarker(coach: coach)
            }
        }
    }
}
```

### 6.3 支付集成
```swift
class PaymentManager: ObservableObject {
    func processPayment(amount: Int, for booking: Booking) async throws {
        // 集成支付宝或微信支付SDK
        // 或使用Apple Pay
    }
}
```

---

## 7. 测试策略

### 7.1 单元测试
```swift
class CoachDiscoveryViewModelTests: XCTestCase {
    var viewModel: CoachDiscoveryViewModel!
    var mockAPIClient: MockAPIClient!
    
    override func setUp() {
        super.setUp()
        mockAPIClient = MockAPIClient()
        viewModel = CoachDiscoveryViewModel(apiClient: mockAPIClient)
    }
    
    func testSearchCoaches() async {
        // 测试搜索功能
    }
}
```

### 7.2 UI测试
```swift
class CoachDiscoveryUITests: XCTestCase {
    func testCoachListDisplay() {
        let app = XCUIApplication()
        app.launch()
        
        // 验证教练列表显示
        XCTAssertTrue(app.collectionViews.firstMatch.exists)
    }
}
```

---

## 8. 部署与发布

### 8.1 构建配置
```swift
// Debug配置
#if DEBUG
let apiBaseURL = "https://dev-api.example.com"
#else
let apiBaseURL = "https://api.example.com"
#endif
```

### 8.2 App Store发布清单
- [ ] 应用图标和截图
- [ ] 应用描述和关键词
- [ ] 隐私政策
- [ ] 年龄分级
- [ ] 应用内购买配置（如需要）
- [ ] TestFlight测试
- [ ] 提交审核

---

## 9. 后续优化方向

### 9.1 性能优化
- 图片缓存和懒加载
- 网络请求优化
- 内存管理
- 启动时间优化

### 9.2 功能扩展
- 离线模式支持
- 社交分享功能
- 智能推荐算法
- 数据分析和统计

### 9.3 用户体验
- 动画效果优化
- 无障碍功能支持
- 多语言支持
- 深色模式适配

这份开发指南为您的iOS应用开发提供了完整的技术路线图，您可以根据团队实际情况和项目需求进行调整和细化。
